{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[\"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"nightly\", \"raw-entry\", \"rayon\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 13796197676120832388, "profile": 2241668132362809309, "path": 15769191755609367331, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hashbrown-218f96c5f6a4c550\\dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}