{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[]", "target": 56689210197507398, "profile": 17672942494452627365, "path": 2995440285629810167, "deps": [[889364689474263285, "reqwest", false, 260284875865200104], [1288403060204016458, "tokio_util", false, 14874804934029588508], [1441306149310335789, "tempfile", false, 12052080928802903040], [2706460456408817945, "futures", false, 14460612508354959342], [4656928804077918400, "flume", false, 790215862676973062], [8256202458064874477, "dirs", false, 16360456654618800164], [8319709847752024821, "uuid", false, 10898875282141399263], [8606274917505247608, "tracing", false, 7118938455684139428], [8786711029710048183, "toml", false, 5523983248528403715], [9451456094439810778, "regex", false, 3793085470722798449], [9538054652646069845, "tokio", false, 3840114820292566334], [9676459985605165425, "arien_common", false, 5666183610118933136], [9689903380558560274, "serde", false, 17690120068839890953], [9857275760291862238, "sha2", false, 10721842450547150920], [9897246384292347999, "chrono", false, 5906534937241977436], [10806645703491011684, "thiserror", false, 1324627398403683915], [12579666021862527596, "arien_mcp_client", false, 957354405587597553], [13028763805764736075, "image", false, 7304744425163690939], [13077212702700853852, "base64", false, 17996518868991860874], [13625485746686963219, "anyhow", false, 17220651729097877131], [15367738274754116744, "serde_json", false, 2738634569744250569], [15622660310229662834, "walkdir", false, 3968250498528908898], [16372542609801848218, "arien_mcp_types", false, 17455370001320853886]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\arien-core-6b1c0fcba549458f\\dep-lib-arien_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}