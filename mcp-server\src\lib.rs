use arien_common::{Result, <PERSON>enError};
use arien_mcp_types::*;
use serde_json::Value;
use std::collections::HashMap;
use tokio::io::{AsyncBufReadExt, AsyncWriteExt};

/// MCP server implementation that exposes Arien as MCP tools
pub struct McpServer {
    capabilities: ServerCapabilities,
    tools: HashMap<String, Tool>,
}

impl McpServer {
    pub fn new() -> Self {
        let mut tools = HashMap::new();

        // Define Arien tools
        tools.insert("arien_chat".to_string(), Tool {
            name: "arien_chat".to_string(),
            description: Some("Chat with Arien AI assistant".to_string()),
            input_schema: ToolInputSchema::object()
                .add_string_property("message", Some("Message to send to Arien".to_string()), true)
                .add_string_property("instructions", Some("Optional system instructions".to_string()), false),
        });

        tools.insert("arien_execute".to_string(), Tool {
            name: "arien_execute".to_string(),
            description: Some("Execute a command through Arien".to_string()),
            input_schema: ToolInputSchema::object()
                .add_string_property("command", Some("Command to execute".to_string()), true)
                .add_string_property("working_dir", Some("Working directory for the command".to_string()), false),
        });

        let capabilities = ServerCapabilities {
            tools: Some(ToolsCapabilities {
                list_changed: Some(false),
            }),
            resources: None,
            prompts: None,
            logging: None,
            experimental: None,
        };

        Self {
            capabilities,
            tools,
        }
    }

    /// Run the MCP server over stdio
    pub async fn run_stdio(&self) -> Result<()> {
        let stdin = tokio::io::stdin();
        let mut stdout = tokio::io::stdout();
        let mut reader = tokio::io::BufReader::new(stdin);
        let mut line = String::new();

        tracing::info!("MCP server started on stdio");

        loop {
            line.clear();
            match reader.read_line(&mut line).await {
                Ok(0) => break, // EOF
                Ok(_) => {
                    let line = line.trim();
                    if line.is_empty() {
                        continue;
                    }

                    // Parse JSON-RPC request
                    match serde_json::from_str::<Value>(line) {
                        Ok(request) => {
                            if let Some(response) = self.handle_request(request).await? {
                                stdout.write_all(format!("{}\n", response).as_bytes()).await?;
                                stdout.flush().await?;
                            }
                        }
                        Err(e) => {
                            let error_response = serde_json::json!({
                                "jsonrpc": "2.0",
                                "error": {
                                    "code": -32700,
                                    "message": "Parse error",
                                    "data": e.to_string()
                                },
                                "id": null
                            });
                            stdout.write_all(format!("{}\n", error_response).as_bytes()).await?;
                            stdout.flush().await?;
                        }
                    }
                }
                Err(e) => {
                    tracing::error!("Error reading from stdin: {}", e);
                    break;
                }
            }
        }

        Ok(())
    }

    async fn handle_request(&self, request: Value) -> Result<Option<Value>> {
        let method = request.get("method")
            .and_then(|m| m.as_str())
            .ok_or_else(|| ArienError::unknown("Missing method"))?;

        let id = request.get("id");

        match method {
            "initialize" => {
                let response = serde_json::json!({
                    "jsonrpc": "2.0",
                    "result": {
                        "protocolVersion": "2024-11-05",
                        "capabilities": self.capabilities,
                        "serverInfo": {
                            "name": "arien-mcp-server",
                            "version": "0.1.0"
                        }
                    },
                    "id": id
                });
                Ok(Some(response))
            }
            "notifications/initialized" => {
                // No response needed for notifications
                Ok(None)
            }
            "tools/list" => {
                let tools: Vec<&Tool> = self.tools.values().collect();
                let response = serde_json::json!({
                    "jsonrpc": "2.0",
                    "result": {
                        "tools": tools
                    },
                    "id": id
                });
                Ok(Some(response))
            }
            "tools/call" => {
                let params = request.get("params")
                    .ok_or_else(|| ArienError::unknown("Missing params"))?;

                let tool_name = params.get("name")
                    .and_then(|n| n.as_str())
                    .ok_or_else(|| ArienError::unknown("Missing tool name"))?;

                let default_args = serde_json::json!({});
                let arguments = params.get("arguments")
                    .unwrap_or(&default_args);

                let result = self.call_tool(tool_name, arguments).await?;

                let response = serde_json::json!({
                    "jsonrpc": "2.0",
                    "result": result,
                    "id": id
                });
                Ok(Some(response))
            }
            _ => {
                let error_response = serde_json::json!({
                    "jsonrpc": "2.0",
                    "error": {
                        "code": -32601,
                        "message": "Method not found",
                        "data": method
                    },
                    "id": id
                });
                Ok(Some(error_response))
            }
        }
    }

    async fn call_tool(&self, tool_name: &str, arguments: &Value) -> Result<Value> {
        match tool_name {
            "arien_chat" => {
                let message = arguments.get("message")
                    .and_then(|m| m.as_str())
                    .ok_or_else(|| ArienError::unknown("Missing message argument"))?;

                let _instructions = arguments.get("instructions")
                    .and_then(|i| i.as_str());

                // This is a simplified implementation
                // In a real implementation, we would create an Arien instance and process the request
                let response_content = format!("Arien would respond to: {}", message);

                Ok(serde_json::json!({
                    "content": [
                        {
                            "type": "text",
                            "text": response_content
                        }
                    ]
                }))
            }
            "arien_execute" => {
                let command = arguments.get("command")
                    .and_then(|c| c.as_str())
                    .ok_or_else(|| ArienError::unknown("Missing command argument"))?;

                let _working_dir = arguments.get("working_dir")
                    .and_then(|w| w.as_str());

                // This is a simplified implementation
                // In a real implementation, we would execute the command through Arien
                let response_content = format!("Arien would execute: {}", command);

                Ok(serde_json::json!({
                    "content": [
                        {
                            "type": "text",
                            "text": response_content
                        }
                    ]
                }))
            }
            _ => {
                Err(ArienError::unknown(format!("Unknown tool: {}", tool_name)))
            }
        }
    }
}

impl Default for McpServer {
    fn default() -> Self {
        Self::new()
    }
}
