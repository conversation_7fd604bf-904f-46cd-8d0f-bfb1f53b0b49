[package]
name = "arien-cli"
version = "0.1.0"
edition = "2024"

[[bin]]
name = "arien"
path = "src/main.rs"

[dependencies]
arien-core = { path = "../core" }
arien-common = { path = "../common" }
arien-mcp-server = { path = "../mcp-server" }
arien-linux-sandbox = { path = "../linux-sandbox" }
clap = { workspace = true }
tokio = { workspace = true }
tracing = { workspace = true }
tracing-subscriber = { workspace = true }
serde_json = { workspace = true }
anyhow = { workspace = true }
