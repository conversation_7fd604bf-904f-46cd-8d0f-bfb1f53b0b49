use clap::{Parser, Subcommand};
use arien_core::{Config, ConfigOverride, Arien, Op, SessionConfig, InputItem};
use arien_common::{SessionId, Result, ArienError};
use std::io::{self, Write};

#[derive(Parser)]
#[command(name = "arien")]
#[command(about = "Arien AI - AI-powered coding assistant")]
#[command(version = "0.1.0")]
struct Cli {
    #[command(subcommand)]
    command: Option<Commands>,

    /// Configuration overrides in key=value format
    #[arg(short = 'c', long = "config", value_parser = ConfigOverride::parse)]
    config_overrides: Vec<ConfigOverride>,

    /// Configuration profile to use
    #[arg(short = 'p', long = "profile")]
    profile: Option<String>,

    /// Log level
    #[arg(long = "log-level", default_value = "info")]
    log_level: String,
}

#[derive(Subcommand)]
enum Commands {
    /// Interactive mode (launches TUI)
    Interactive,
    
    /// Execute a single prompt and exit
    Exec {
        /// The prompt to execute
        prompt: String,
        
        /// Working directory
        #[arg(short = 'd', long = "dir")]
        working_dir: Option<String>,
        
        /// Auto-approve all operations
        #[arg(long = "auto-approve")]
        auto_approve: bool,
    },
    
    /// Run as MCP server
    McpServer,
    
    /// Protocol mode (JSON over stdin/stdout)
    Protocol,
    
    /// Debug commands
    Debug {
        #[command(subcommand)]
        debug_command: DebugCommands,
    },
}

#[derive(Subcommand)]
enum DebugCommands {
    /// Test sandbox functionality
    TestSandbox {
        /// Command to test
        command: Vec<String>,
    },
    
    /// Show configuration
    ShowConfig,
    
    /// List available MCP tools
    ListTools,
}

#[tokio::main]
async fn main() -> Result<()> {
    let cli = Cli::parse();

    // Initialize logging
    tracing_subscriber::fmt()
        .with_env_filter(tracing_subscriber::EnvFilter::new(&cli.log_level))
        .init();

    // Load configuration
    let config = Config::load_with_overrides(cli.config_overrides, cli.profile).await?;

    match cli.command {
        Some(Commands::Interactive) => {
            interactive_mode(config).await
        }
        Some(Commands::Exec { prompt, working_dir, auto_approve }) => {
            exec_mode(config, prompt, working_dir, auto_approve).await
        }
        Some(Commands::McpServer) => {
            mcp_server_mode(config).await
        }
        Some(Commands::Protocol) => {
            protocol_mode(config).await
        }
        Some(Commands::Debug { debug_command }) => {
            debug_mode(config, debug_command).await
        }
        None => {
            // Default to interactive mode
            interactive_mode(config).await
        }
    }
}

async fn interactive_mode(config: Config) -> Result<()> {
    println!("🤖 Arien AI - Interactive Mode");
    println!("Type 'help' for commands, 'quit' to exit");
    
    let arien = Arien::new(config).await?;
    let session_id = SessionId::new();
    
    // Configure session
    let session_config = SessionConfig {
        model: None,
        model_provider: None,
        instructions: Some("You are a helpful AI coding assistant.".to_string()),
        approval_policy: None,
        sandbox_policy: None,
        shell_environment_policy: None,
        writable_roots: None,
        mcp_servers: None,
    };
    
    arien.submit(session_id, Op::ConfigureSession { config: session_config }).await?;
    
    // Wait for session configured event
    let event = arien.recv_event().await?;
    println!("Session configured: {:?}", event);

    loop {
        print!("arien> ");
        io::stdout().flush().unwrap();
        
        let mut input = String::new();
        io::stdin().read_line(&mut input).unwrap();
        let input = input.trim();
        
        if input.is_empty() {
            continue;
        }
        
        match input {
            "quit" | "exit" => break,
            "help" => {
                println!("Commands:");
                println!("  help    - Show this help");
                println!("  quit    - Exit the program");
                println!("  <text>  - Send message to AI");
            }
            _ => {
                // Send user input
                let items = vec![InputItem::Text {
                    content: input.to_string(),
                }];
                
                arien.submit(session_id, Op::UserInput { items }).await?;
                
                // Process events
                loop {
                    let event = arien.recv_event().await?;
                    match event.event_type {
                        arien_core::EventType::ModelResponse { content, partial } => {
                            if partial {
                                print!("{}", content);
                                io::stdout().flush().unwrap();
                            } else {
                                println!("{}", content);
                            }
                        }
                        arien_core::EventType::ConversationEnded => {
                            break;
                        }
                        arien_core::EventType::Error { error, .. } => {
                            eprintln!("Error: {}", error);
                            break;
                        }
                        _ => {
                            println!("Event: {:?}", event.event_type);
                        }
                    }
                }
            }
        }
    }
    
    println!("Goodbye! 👋");
    Ok(())
}

async fn exec_mode(config: Config, prompt: String, _working_dir: Option<String>, _auto_approve: bool) -> Result<()> {
    println!("🤖 Arien AI - Exec Mode");
    
    let arien = Arien::new(config).await?;
    let session_id = SessionId::new();
    
    // Configure session for non-interactive mode
    let session_config = SessionConfig {
        model: None,
        model_provider: None,
        instructions: Some("You are a helpful AI coding assistant. Be concise and direct.".to_string()),
        approval_policy: Some(arien_core::AskForApproval::Never),
        sandbox_policy: None,
        shell_environment_policy: None,
        writable_roots: None,
        mcp_servers: None,
    };
    
    arien.submit(session_id, Op::ConfigureSession { config: session_config }).await?;
    
    // Wait for session configured
    arien.recv_event().await?;
    
    // Send prompt
    let items = vec![InputItem::Text { content: prompt }];
    arien.submit(session_id, Op::UserInput { items }).await?;
    
    // Process events until conversation ends
    loop {
        let event = arien.recv_event().await?;
        match event.event_type {
            arien_core::EventType::ModelResponse { content, partial } => {
                if partial {
                    print!("{}", content);
                    io::stdout().flush().unwrap();
                } else {
                    println!("{}", content);
                }
            }
            arien_core::EventType::ConversationEnded => {
                break;
            }
            arien_core::EventType::Error { error, .. } => {
                eprintln!("Error: {}", error);
                return Err(ArienError::unknown(error));
            }
            _ => {
                // Log other events in exec mode
                tracing::debug!("Event: {:?}", event.event_type);
            }
        }
    }
    
    Ok(())
}

async fn mcp_server_mode(_config: Config) -> Result<()> {
    tracing::info!("Starting Arien AI as MCP server");

    let server = arien_mcp_server::McpServer::new();
    server.run_stdio().await?;

    Ok(())
}

async fn protocol_mode(config: Config) -> Result<()> {
    use tokio::io::{AsyncBufReadExt, AsyncWriteExt};

    tracing::info!("Starting Arien AI in protocol mode");

    let arien = Arien::new(config).await?;
    let session_id = SessionId::new();

    // Configure session for protocol mode
    let session_config = SessionConfig {
        model: None,
        model_provider: None,
        instructions: Some("You are a helpful AI coding assistant operating in protocol mode.".to_string()),
        approval_policy: Some(arien_core::AskForApproval::Never), // Auto-approve in protocol mode
        sandbox_policy: None,
        shell_environment_policy: None,
        writable_roots: None,
        mcp_servers: None,
    };

    arien.submit(session_id, Op::ConfigureSession { config: session_config }).await?;

    // Wait for session configured
    arien.recv_event().await?;

    let stdin = tokio::io::stdin();
    let mut stdout = tokio::io::stdout();
    let mut reader = tokio::io::BufReader::new(stdin);
    let mut line = String::new();

    // Send ready signal
    let ready_response = serde_json::json!({
        "type": "ready",
        "version": "0.1.0"
    });
    stdout.write_all(format!("{}\n", ready_response).as_bytes()).await?;
    stdout.flush().await?;

    loop {
        line.clear();
        match reader.read_line(&mut line).await {
            Ok(0) => break, // EOF
            Ok(_) => {
                let line = line.trim();
                if line.is_empty() {
                    continue;
                }

                // Parse JSON request
                match serde_json::from_str::<serde_json::Value>(line) {
                    Ok(request) => {
                        if let Err(e) = handle_protocol_request(&arien, session_id, request, &mut stdout).await {
                            let error_response = serde_json::json!({
                                "type": "error",
                                "error": e.to_string()
                            });
                            stdout.write_all(format!("{}\n", error_response).as_bytes()).await?;
                            stdout.flush().await?;
                        }
                    }
                    Err(e) => {
                        let error_response = serde_json::json!({
                            "type": "error",
                            "error": format!("Invalid JSON: {}", e)
                        });
                        stdout.write_all(format!("{}\n", error_response).as_bytes()).await?;
                        stdout.flush().await?;
                    }
                }
            }
            Err(e) => {
                tracing::error!("Error reading from stdin: {}", e);
                break;
            }
        }
    }

    Ok(())
}

async fn handle_protocol_request(
    arien: &Arien,
    session_id: SessionId,
    request: serde_json::Value,
    stdout: &mut tokio::io::Stdout,
) -> Result<()> {
    use tokio::io::AsyncWriteExt;

    let request_type = request.get("type")
        .and_then(|t| t.as_str())
        .ok_or_else(|| ArienError::unknown("Missing request type"))?;

    match request_type {
        "submit" => {
            let content = request.get("content")
                .and_then(|c| c.as_str())
                .ok_or_else(|| ArienError::unknown("Missing content"))?;

            let items = vec![InputItem::Text {
                content: content.to_string(),
            }];

            arien.submit(session_id, Op::UserInput { items }).await?;

            // Process events and send responses
            loop {
                let event = arien.recv_event().await?;
                match event.event_type {
                    arien_core::EventType::ModelResponse { content, partial } => {
                        let response = serde_json::json!({
                            "type": "response",
                            "content": content,
                            "partial": partial
                        });
                        stdout.write_all(format!("{}\n", response).as_bytes()).await?;
                        stdout.flush().await?;
                    }
                    arien_core::EventType::ConversationEnded => {
                        let response = serde_json::json!({
                            "type": "completed"
                        });
                        stdout.write_all(format!("{}\n", response).as_bytes()).await?;
                        stdout.flush().await?;
                        break;
                    }
                    arien_core::EventType::Error { error, .. } => {
                        let response = serde_json::json!({
                            "type": "error",
                            "error": error
                        });
                        stdout.write_all(format!("{}\n", response).as_bytes()).await?;
                        stdout.flush().await?;
                        break;
                    }
                    _ => {
                        // Send other events as info
                        let response = serde_json::json!({
                            "type": "event",
                            "event": format!("{:?}", event.event_type)
                        });
                        stdout.write_all(format!("{}\n", response).as_bytes()).await?;
                        stdout.flush().await?;
                    }
                }
            }
        }
        "ping" => {
            let response = serde_json::json!({
                "type": "pong"
            });
            stdout.write_all(format!("{}\n", response).as_bytes()).await?;
            stdout.flush().await?;
        }
        _ => {
            return Err(ArienError::unknown(format!("Unknown request type: {}", request_type)));
        }
    }

    Ok(())
}

async fn debug_mode(config: Config, debug_command: DebugCommands) -> Result<()> {
    match debug_command {
        DebugCommands::TestSandbox { command } => {
            println!("🔍 Testing sandbox with command: {:?}", command);

            if command.is_empty() {
                println!("Error: No command provided");
                return Ok(());
            }

            let sandbox = arien_linux_sandbox::LinuxSandbox::new()
                .allow_path(&std::env::current_dir().unwrap_or_else(|_| std::path::PathBuf::from(".")))
                .allow_path("/tmp");

            let program = &command[0];
            let args = if command.len() > 1 { &command[1..] } else { &[] };

            println!("Executing in sandbox: {} {:?}", program, args);

            match sandbox.execute_sandboxed(program, args, Some(&std::env::current_dir().unwrap_or_else(|_| std::path::PathBuf::from(".")))).await {
                Ok(mut process) => {
                    match process.wait_with_output().await {
                        Ok(output) => {
                            println!("✅ Command completed with exit code: {}", output.exit_code());
                            if !output.stdout_string().is_empty() {
                                println!("📤 Stdout:\n{}", output.stdout_string());
                            }
                            if !output.stderr_string().is_empty() {
                                println!("📤 Stderr:\n{}", output.stderr_string());
                            }
                        }
                        Err(e) => {
                            println!("❌ Error waiting for process: {}", e);
                        }
                    }
                }
                Err(e) => {
                    println!("❌ Error executing command in sandbox: {}", e);
                }
            }
        }
        DebugCommands::ShowConfig => {
            println!("🔧 Current configuration:");
            println!("{:#?}", config);
        }
        DebugCommands::ListTools => {
            println!("🛠️  Available MCP tools:");
            println!("MCP tool listing functionality will be implemented when MCP connection manager is complete");
            println!("For now, you can test the MCP server mode with: arien mcp-server");
        }
    }
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_cli_parsing() {
        // Test basic command parsing
        let cli = Cli::try_parse_from(&["arien", "exec", "hello world"]).unwrap();
        
        match cli.command {
            Some(Commands::Exec { prompt, .. }) => {
                assert_eq!(prompt, "hello world");
            }
            _ => panic!("Expected exec command"),
        }
    }

    #[test]
    fn test_config_override_parsing() {
        let cli = Cli::try_parse_from(&[
            "arien", 
            "-c", "model=gpt-4",
            "-c", "approval_policy=never",
            "interactive"
        ]).unwrap();
        
        assert_eq!(cli.config_overrides.len(), 2);
        assert_eq!(cli.config_overrides[0].key, "model");
        assert_eq!(cli.config_overrides[0].value, "gpt-4");
    }
}
