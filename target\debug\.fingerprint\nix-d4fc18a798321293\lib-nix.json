{"rustc": 10895048813736897673, "features": "[\"default\"]", "declared_features": "[\"acct\", \"aio\", \"default\", \"dir\", \"env\", \"event\", \"fanotify\", \"feature\", \"fs\", \"hostname\", \"inotify\", \"ioctl\", \"kmod\", \"memoffset\", \"mman\", \"mount\", \"mqueue\", \"net\", \"personality\", \"pin-utils\", \"poll\", \"process\", \"pthread\", \"ptrace\", \"quota\", \"reboot\", \"resource\", \"sched\", \"signal\", \"socket\", \"term\", \"time\", \"ucontext\", \"uio\", \"user\", \"zerocopy\"]", "target": 2594889627657062481, "profile": 2241668132362809309, "path": 10687164904156591953, "deps": [[2924422107542798392, "libc", false, 7877930043448493491], [5452785045801004098, "build_script_build", false, 458439031998141778], [7896293946984509699, "bitflags", false, 6230422252411917668], [10411997081178400487, "cfg_if", false, 17451303736681025148]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\nix-d4fc18a798321293\\dep-lib-nix", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}