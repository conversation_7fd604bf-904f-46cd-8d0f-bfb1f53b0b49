use std::collections::HashMap;
use std::path::PathBuf;
use std::sync::Arc;
use tokio::sync::{Notify, Mutex};
use flume::Sender;
use arien_common::{Result, ArienError, SessionId, SafePath};
use crate::{
    Config, Event, EventType, Submission, Op, ModelClient, McpConnectionManager,
    AskForApproval, SandboxPolicy, ShellEnvironmentPolicy, SessionConfig,
    InputItem, FileChange,
};

/// Session manages the state and configuration for a single AI interaction session
pub struct Session {
    session_id: SessionId,
    client: ModelClient,
    tx_event: Sender<Event>,
    ctrl_c: Arc<Notify>,
    cwd: PathBuf,
    instructions: Option<String>,
    approval_policy: AskForApproval,
    sandbox_policy: SandboxPolicy,
    shell_environment_policy: ShellEnvironmentPolicy,
    writable_roots: Mutex<Vec<PathBuf>>,
    mcp_connection_manager: McpConnectionManager,
    conversation_history: Mutex<Vec<ConversationMessage>>,
    pending_approvals: Mutex<HashMap<String, PendingApproval>>,
}

/// Conversation message
#[derive(Debug, Clone)]
pub struct ConversationMessage {
    pub role: MessageRole,
    pub content: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

/// Message role
#[derive(Debug, Clone)]
pub enum MessageRole {
    User,
    Assistant,
    System,
}

/// Pending approval
#[derive(Debug, Clone)]
pub enum PendingApproval {
    Execution {
        command: Vec<String>,
        working_dir: Option<SafePath>,
    },
    Patch {
        patch_id: String,
        changes: Vec<FileChange>,
    },
    ToolCall {
        tool_call_id: String,
        tool_name: String,
        arguments: HashMap<String, serde_json::Value>,
    },
}

impl Session {
    /// Create a new session
    pub async fn new(
        session_id: SessionId,
        tx_event: Sender<Event>,
        ctrl_c: Arc<Notify>,
        config: Config,
    ) -> Result<Self> {
        let client = ModelClient::new(config.clone()).await?;
        let mcp_connection_manager = McpConnectionManager::new(config.mcp_servers.clone()).await?;
        
        let cwd = std::env::current_dir()
            .map_err(|e| ArienError::config(format!("Failed to get current directory: {}", e)))?;

        Ok(Self {
            session_id,
            client,
            tx_event,
            ctrl_c,
            cwd,
            instructions: config.instructions,
            approval_policy: config.approval_policy,
            sandbox_policy: config.sandbox_policy,
            shell_environment_policy: config.shell_environment_policy,
            writable_roots: Mutex::new(config.writable_roots),
            mcp_connection_manager,
            conversation_history: Mutex::new(Vec::new()),
            pending_approvals: Mutex::new(HashMap::new()),
        })
    }

    /// Handle a submission
    pub async fn handle_submission(&mut self, submission: Submission) -> Result<()> {
        match submission.op {
            Op::ConfigureSession { config } => {
                self.configure_session(submission.id, config).await
            }
            Op::UserInput { items } => {
                self.handle_user_input(submission.id, items).await
            }
            Op::ExecApproval { approved, command } => {
                self.handle_exec_approval(submission.id, approved, command).await
            }
            Op::PatchApproval { approved, patch_id } => {
                self.handle_patch_approval(submission.id, approved, patch_id).await
            }
            Op::ToolApproval { approved, tool_call_id } => {
                self.handle_tool_approval(submission.id, approved, tool_call_id).await
            }
            Op::Interrupt => {
                self.handle_interrupt(submission.id).await
            }
            Op::Reset => {
                self.handle_reset(submission.id).await
            }
        }
    }

    async fn configure_session(&mut self, submission_id: arien_common::SubmissionId, config: SessionConfig) -> Result<()> {
        // Apply session configuration
        if let Some(instructions) = config.instructions {
            self.instructions = Some(instructions);
        }
        
        if let Some(approval_policy) = config.approval_policy {
            self.approval_policy = approval_policy;
        }
        
        if let Some(sandbox_policy) = config.sandbox_policy {
            self.sandbox_policy = sandbox_policy;
        }
        
        if let Some(shell_environment_policy) = config.shell_environment_policy {
            self.shell_environment_policy = shell_environment_policy;
        }
        
        if let Some(writable_roots) = config.writable_roots {
            let mut roots = self.writable_roots.lock().await;
            *roots = writable_roots.into_iter().map(|p| p.into_path_buf()).collect();
        }

        // Update model if specified
        if let Some(model) = config.model {
            self.client.set_model(model).await?;
        }

        if let Some(model_provider) = config.model_provider {
            self.client.set_provider(model_provider).await?;
        }

        // Update MCP servers if specified
        if let Some(mcp_servers) = config.mcp_servers {
            self.mcp_connection_manager.update_servers(mcp_servers).await?;
        }

        // Send session configured event
        self.send_event(submission_id, EventType::SessionConfigured {
            session_id: self.session_id.clone(),
        }).await
    }

    async fn handle_user_input(&mut self, submission_id: arien_common::SubmissionId, items: Vec<InputItem>) -> Result<()> {
        // Add user input to conversation history
        for item in &items {
            match item {
                InputItem::Text { content } => {
                    self.add_to_conversation(MessageRole::User, content.clone()).await;
                }
                InputItem::Image { .. } => {
                    self.add_to_conversation(MessageRole::User, "[Image]".to_string()).await;
                }
                InputItem::LocalImage { path } => {
                    self.add_to_conversation(MessageRole::User, format!("[Image: {}]", path)).await;
                }
                InputItem::File { path, .. } => {
                    self.add_to_conversation(MessageRole::User, format!("[File: {}]", path)).await;
                }
            }
        }

        // Send conversation started event
        self.send_event(submission_id, EventType::ConversationStarted).await?;

        // Process the input with the model
        self.process_with_model(submission_id, items).await
    }

    async fn handle_exec_approval(&mut self, submission_id: arien_common::SubmissionId, approved: bool, command: Vec<String>) -> Result<()> {
        let approval_key = format!("exec:{}", command.join(" "));

        let mut pending = self.pending_approvals.lock().await;

        if let Some(PendingApproval::Execution { command: pending_command, working_dir }) = pending.remove(&approval_key) {
            drop(pending);

            if approved && command == pending_command {
                self.execute_command(submission_id, command, working_dir).await
            } else {
                self.send_event(submission_id, EventType::Info {
                    message: "Command execution cancelled by user".to_string(),
                }).await
            }
        } else {
            self.send_event(submission_id, EventType::Warning {
                message: "No pending execution found for approval".to_string(),
            }).await
        }
    }

    async fn handle_patch_approval(&mut self, submission_id: arien_common::SubmissionId, approved: bool, patch_id: String) -> Result<()> {
        let mut pending = self.pending_approvals.lock().await;

        if let Some(PendingApproval::Patch { patch_id: pending_id, changes }) = pending.remove(&patch_id) {
            drop(pending);

            if approved && patch_id == pending_id {
                self.apply_patch(submission_id, patch_id, changes).await
            } else {
                self.send_event(submission_id, EventType::Info {
                    message: "Patch application cancelled by user".to_string(),
                }).await
            }
        } else {
            self.send_event(submission_id, EventType::Warning {
                message: "No pending patch found for approval".to_string(),
            }).await
        }
    }

    async fn handle_tool_approval(&mut self, submission_id: arien_common::SubmissionId, approved: bool, tool_call_id: arien_common::ToolCallId) -> Result<()> {
        let tool_call_id_str = tool_call_id.to_string();

        let mut pending = self.pending_approvals.lock().await;

        if let Some(PendingApproval::ToolCall { tool_call_id: pending_id, tool_name, arguments }) = pending.remove(&tool_call_id_str) {
            drop(pending);

            if approved && tool_call_id_str == pending_id {
                self.execute_tool_call(submission_id, tool_call_id, tool_name, arguments).await
            } else {
                self.send_event(submission_id, EventType::Info {
                    message: "Tool call cancelled by user".to_string(),
                }).await
            }
        } else {
            self.send_event(submission_id, EventType::Warning {
                message: "No pending tool call found for approval".to_string(),
            }).await
        }
    }

    async fn handle_interrupt(&mut self, submission_id: arien_common::SubmissionId) -> Result<()> {
        // Clear pending approvals
        {
            let mut pending = self.pending_approvals.lock().await;
            pending.clear();
        }

        // Send interrupt signal
        self.ctrl_c.notify_waiters();

        self.send_event(submission_id, EventType::Info {
            message: "Session interrupted".to_string(),
        }).await
    }

    async fn handle_reset(&mut self, submission_id: arien_common::SubmissionId) -> Result<()> {
        // Clear conversation history
        {
            let mut history = self.conversation_history.lock().await;
            history.clear();
        }

        // Clear pending approvals
        {
            let mut pending = self.pending_approvals.lock().await;
            pending.clear();
        }

        self.send_event(submission_id, EventType::Info {
            message: "Session reset".to_string(),
        }).await
    }

    async fn process_with_model(&mut self, submission_id: arien_common::SubmissionId, items: Vec<InputItem>) -> Result<()> {
        // Build conversation context
        let mut messages = Vec::new();

        // Add system message if we have instructions
        if let Some(instructions) = &self.instructions {
            messages.push(crate::ChatMessage::system(instructions));
        }

        // Add conversation history
        {
            let history = self.conversation_history.lock().await;
            for msg in history.iter() {
                let role = match msg.role {
                    MessageRole::User => "user",
                    MessageRole::Assistant => "assistant",
                    MessageRole::System => "system",
                };
                messages.push(crate::ChatMessage {
                    role: role.to_string(),
                    content: msg.content.clone(),
                });
            }
        }

        // Add current user input
        for item in &items {
            match item {
                InputItem::Text { content } => {
                    messages.push(crate::ChatMessage::user(content));
                }
                InputItem::Image { .. } => {
                    messages.push(crate::ChatMessage::user("[Image content]"));
                }
                InputItem::LocalImage { path } => {
                    messages.push(crate::ChatMessage::user(format!("[Image: {}]", path)));
                }
                InputItem::File { path, .. } => {
                    // Read file content if it's a text file
                    if let Ok(content) = tokio::fs::read_to_string(path.as_path()).await {
                        messages.push(crate::ChatMessage::user(format!("File {}: {}", path, content)));
                    } else {
                        messages.push(crate::ChatMessage::user(format!("[File: {}]", path)));
                    }
                }
            }
        }

        // Stream response from model
        match self.client.stream_chat_completion(messages).await {
            Ok(mut stream) => {
                use futures::StreamExt;
                let mut full_response = String::new();

                while let Some(chunk_result) = stream.next().await {
                    match chunk_result {
                        Ok(chunk) => {
                            if !chunk.is_empty() {
                                full_response.push_str(&chunk);

                                // Send partial response
                                self.send_event(submission_id, EventType::ModelResponse {
                                    content: chunk,
                                    partial: true,
                                }).await?;
                            }
                        }
                        Err(e) => {
                            self.send_event(submission_id, EventType::Error {
                                error: format!("Streaming error: {}", e),
                                recoverable: true,
                            }).await?;
                            return Ok(());
                        }
                    }
                }

                // Send final response
                self.send_event(submission_id, EventType::ModelResponse {
                    content: String::new(), // Empty content to signal end
                    partial: false,
                }).await?;

                // Add assistant response to conversation
                self.add_to_conversation(MessageRole::Assistant, full_response.clone()).await;

                // Parse response for tool calls and commands
                self.parse_and_execute_response(submission_id, &full_response).await?;

                self.send_event(submission_id, EventType::ConversationEnded).await
            }
            Err(e) => {
                self.send_event(submission_id, EventType::Error {
                    error: format!("Model error: {}", e),
                    recoverable: true,
                }).await
            }
        }
    }

    async fn execute_command(&mut self, submission_id: arien_common::SubmissionId, command: Vec<String>, working_dir: Option<SafePath>) -> Result<()> {
        let executor = crate::CommandExecutor::new(
            self.sandbox_policy.clone(),
            self.approval_policy,
        );

        let work_dir = working_dir.as_ref().map(|p| p.as_path().to_path_buf()).or_else(|| Some(self.cwd.clone()));
        let safe_work_dir = working_dir.unwrap_or_else(|| SafePath::new(&self.cwd).unwrap());

        self.send_event(submission_id, EventType::ExecStarted {
            command: command.clone(),
            working_dir: safe_work_dir,
        }).await?;

        // Execute with streaming output
        let result = executor.execute_streaming(
            command.clone(),
            work_dir,
            |_output, _is_stderr| {
                // Send output events (we can't await here, so we'll need to handle this differently)
                // For now, we'll collect output and send it at the end
            }
        ).await;

        match result {
            Ok(crate::ExecutionResult::Completed { exit_code, stdout, stderr, duration_ms }) => {
                // Send stdout
                if !stdout.is_empty() {
                    self.send_event(submission_id, EventType::ExecOutput {
                        output: stdout,
                        is_stderr: false,
                    }).await?;
                }

                // Send stderr
                if !stderr.is_empty() {
                    self.send_event(submission_id, EventType::ExecOutput {
                        output: stderr,
                        is_stderr: true,
                    }).await?;
                }

                self.send_event(submission_id, EventType::ExecCompleted {
                    exit_code,
                    duration_ms,
                }).await
            }
            Ok(crate::ExecutionResult::NeedsApproval { command, working_dir }) => {
                let safe_working_dir = working_dir.and_then(|p| SafePath::new(p).ok());
                self.send_event(submission_id, EventType::ExecRequest {
                    command,
                    working_dir: safe_working_dir,
                    requires_approval: true,
                }).await
            }
            Ok(crate::ExecutionResult::Failed { error }) => {
                self.send_event(submission_id, EventType::Error {
                    error,
                    recoverable: true,
                }).await
            }
            Err(e) => {
                self.send_event(submission_id, EventType::Error {
                    error: format!("Execution error: {}", e),
                    recoverable: true,
                }).await
            }
        }
    }

    async fn apply_patch(&mut self, submission_id: arien_common::SubmissionId, patch_id: String, changes: Vec<FileChange>) -> Result<()> {
        let writable_roots = {
            let roots = self.writable_roots.lock().await;
            roots.clone()
        };

        let applicator = crate::PatchApplicator::new(writable_roots);

        match applicator.apply_changes(changes).await {
            Ok(results) => {
                self.send_event(submission_id, EventType::PatchApplied {
                    patch_id,
                    results,
                }).await
            }
            Err(e) => {
                self.send_event(submission_id, EventType::Error {
                    error: format!("Patch application failed: {}", e),
                    recoverable: true,
                }).await
            }
        }
    }

    async fn execute_tool_call(&mut self, submission_id: arien_common::SubmissionId, tool_call_id: arien_common::ToolCallId, tool_name: String, arguments: HashMap<String, serde_json::Value>) -> Result<()> {
        // Try to parse as fully-qualified tool name (server::tool)
        let result = if let Some((server_name, actual_tool_name)) = McpConnectionManager::parse_fully_qualified_tool_name(&tool_name) {
            // Call tool on specific server
            let request = arien_mcp_types::CallToolRequest {
                name: actual_tool_name,
                arguments: Some(arguments),
            };

            self.mcp_connection_manager.call_tool(&server_name, request).await
        } else {
            // Try to find the tool on any server
            let request = arien_mcp_types::CallToolRequest {
                name: tool_name.clone(),
                arguments: Some(arguments),
            };

            // Get all tools and find the server that has this tool
            let all_tools = self.mcp_connection_manager.list_all_tools().await?;
            let server_name = all_tools.iter()
                .find(|(_, tool)| tool.name == tool_name)
                .map(|(server, _)| server.clone());

            if let Some(server) = server_name {
                self.mcp_connection_manager.call_tool(&server, request).await
            } else {
                Err(ArienError::mcp(format!("Tool '{}' not found on any server", tool_name)))
            }
        };

        match result {
            Ok(response) => {
                let tool_result = if response.is_error.unwrap_or(false) {
                    let error_content = response.content.into_iter()
                        .map(|c| match c {
                            arien_mcp_types::ToolResponseContent::Text { text } => text,
                            _ => "[Non-text content]".to_string(),
                        })
                        .collect::<Vec<_>>()
                        .join("\n");

                    crate::ToolResult::error(error_content)
                } else {
                    let success_content = response.content.into_iter()
                        .map(|c| match c {
                            arien_mcp_types::ToolResponseContent::Text { text } => text,
                            arien_mcp_types::ToolResponseContent::Image { data, mime_type } => {
                                format!("[Image: {} ({})]", mime_type, data.len())
                            }
                            arien_mcp_types::ToolResponseContent::Resource { resource } => {
                                format!("[Resource: {}]", resource.uri)
                            }
                        })
                        .collect::<Vec<_>>()
                        .join("\n");

                    crate::ToolResult::success(success_content)
                };

                self.send_event(submission_id, EventType::ToolResult {
                    tool_call_id,
                    result: tool_result,
                }).await
            }
            Err(e) => {
                self.send_event(submission_id, EventType::ToolResult {
                    tool_call_id,
                    result: crate::ToolResult::error(format!("Tool execution failed: {}", e)),
                }).await
            }
        }
    }

    async fn send_event(&self, submission_id: arien_common::SubmissionId, event_type: EventType) -> Result<()> {
        let event = Event::new(submission_id, self.session_id.clone(), event_type);
        self.tx_event.send_async(event).await
            .map_err(|_| ArienError::InternalAgentDied)
    }

    async fn add_to_conversation(&self, role: MessageRole, content: String) {
        let mut history = self.conversation_history.lock().await;
        history.push(ConversationMessage {
            role,
            content,
            timestamp: chrono::Utc::now(),
        });
    }

    /// Parse the model response for tool calls, commands, and patches
    async fn parse_and_execute_response(&mut self, submission_id: arien_common::SubmissionId, response: &str) -> Result<()> {
        // Look for tool calls in the response
        if let Some(tool_calls) = self.extract_tool_calls(response) {
            for tool_call in tool_calls {
                self.handle_tool_call(submission_id, tool_call).await?;
            }
        }

        // Look for shell commands in the response
        if let Some(commands) = self.extract_shell_commands(response) {
            for command in commands {
                self.handle_command_execution(submission_id, command).await?;
            }
        }

        // Look for file patches in the response
        if let Some(patches) = self.extract_file_patches(response) {
            for patch in patches {
                self.handle_patch_application(submission_id, patch).await?;
            }
        }

        Ok(())
    }

    /// Extract tool calls from the response text
    fn extract_tool_calls(&self, response: &str) -> Option<Vec<ToolCallInfo>> {
        // This is a simplified parser - in a real implementation, this would
        // parse structured function calls from the model response
        let mut tool_calls = Vec::new();

        // Look for patterns like: <tool_call name="tool_name" args='{"key": "value"}'>
        let tool_call_regex = regex::Regex::new(r#"<tool_call\s+name="([^"]+)"\s+args='([^']+)'>"#).ok()?;

        for captures in tool_call_regex.captures_iter(response) {
            if let (Some(name), Some(args_str)) = (captures.get(1), captures.get(2)) {
                if let Ok(args) = serde_json::from_str::<std::collections::HashMap<String, serde_json::Value>>(args_str.as_str()) {
                    tool_calls.push(ToolCallInfo {
                        id: arien_common::ToolCallId::new(uuid::Uuid::new_v4().to_string()),
                        name: name.as_str().to_string(),
                        arguments: args,
                    });
                }
            }
        }

        if tool_calls.is_empty() {
            None
        } else {
            Some(tool_calls)
        }
    }

    /// Extract shell commands from the response text
    fn extract_shell_commands(&self, response: &str) -> Option<Vec<Vec<String>>> {
        let mut commands = Vec::new();

        // Look for code blocks with shell/bash language
        let code_block_regex = regex::Regex::new(r"```(?:shell|bash|sh)\n(.*?)\n```").ok()?;

        for captures in code_block_regex.captures_iter(response) {
            if let Some(command_text) = captures.get(1) {
                // Split into individual commands
                for line in command_text.as_str().lines() {
                    let line = line.trim();
                    if !line.is_empty() && !line.starts_with('#') {
                        // Simple command parsing - split by whitespace
                        let parts: Vec<String> = line.split_whitespace().map(|s| s.to_string()).collect();
                        if !parts.is_empty() {
                            commands.push(parts);
                        }
                    }
                }
            }
        }

        if commands.is_empty() {
            None
        } else {
            Some(commands)
        }
    }

    /// Extract file patches from the response text
    fn extract_file_patches(&self, response: &str) -> Option<Vec<PatchInfo>> {
        let mut patches = Vec::new();

        // Look for file creation/modification patterns
        let file_patch_regex = regex::Regex::new(r"```(?:diff|patch)\n(.*?)\n```").ok()?;

        for captures in file_patch_regex.captures_iter(response) {
            if let Some(patch_text) = captures.get(1) {
                if let Ok(changes) = crate::PatchParser::parse_unified_diff(patch_text.as_str()) {
                    patches.push(PatchInfo {
                        id: uuid::Uuid::new_v4().to_string(),
                        description: "Model-generated patch".to_string(),
                        changes,
                    });
                }
            }
        }

        if patches.is_empty() {
            None
        } else {
            Some(patches)
        }
    }

    async fn handle_tool_call(&mut self, submission_id: arien_common::SubmissionId, tool_call: ToolCallInfo) -> Result<()> {
        // Check if approval is needed
        let needs_approval = match self.approval_policy {
            AskForApproval::Never => false,
            AskForApproval::AutoEdit => false, // Auto-approve tool calls
            AskForApproval::OnFailure => false, // Auto-approve, escalate on failure
            AskForApproval::UnlessAllowListed => true, // Always ask for approval
        };

        if needs_approval {
            // Store pending approval
            {
                let mut pending = self.pending_approvals.lock().await;
                pending.insert(
                    tool_call.id.to_string(),
                    PendingApproval::ToolCall {
                        tool_call_id: tool_call.id.to_string(),
                        tool_name: tool_call.name.clone(),
                        arguments: tool_call.arguments.clone(),
                    },
                );
            }

            // Send tool call request event
            self.send_event(submission_id, EventType::ToolCall {
                tool_call_id: tool_call.id.clone(),
                tool_name: tool_call.name,
                arguments: tool_call.arguments,
            }).await
        } else {
            // Execute immediately
            self.execute_tool_call(submission_id, tool_call.id, tool_call.name, tool_call.arguments).await
        }
    }

    async fn handle_command_execution(&mut self, submission_id: arien_common::SubmissionId, command: Vec<String>) -> Result<()> {
        let executor = crate::CommandExecutor::new(
            self.sandbox_policy.clone(),
            self.approval_policy,
        );

        match executor.execute(command.clone(), Some(self.cwd.clone())).await? {
            crate::ExecutionResult::NeedsApproval { command, working_dir } => {
                // Store pending approval
                let safe_working_dir = working_dir.as_ref().and_then(|p| SafePath::new(p).ok());
                {
                    let mut pending = self.pending_approvals.lock().await;
                    let approval_key = format!("exec:{}", command.join(" "));
                    pending.insert(
                        approval_key,
                        PendingApproval::Execution {
                            command: command.clone(),
                            working_dir: safe_working_dir.clone(),
                        },
                    );
                }

                // Send execution request event
                self.send_event(submission_id, EventType::ExecRequest {
                    command,
                    working_dir: safe_working_dir,
                    requires_approval: true,
                }).await
            }
            result => {
                // Handle the result directly
                self.handle_execution_result(submission_id, result).await
            }
        }
    }

    async fn handle_patch_application(&mut self, submission_id: arien_common::SubmissionId, patch: PatchInfo) -> Result<()> {
        // Check if approval is needed
        let needs_approval = match self.approval_policy {
            AskForApproval::Never => false,
            AskForApproval::AutoEdit => false, // Auto-approve file edits
            AskForApproval::OnFailure => false, // Auto-approve, escalate on failure
            AskForApproval::UnlessAllowListed => true, // Always ask for approval
        };

        if needs_approval {
            // Store pending approval
            {
                let mut pending = self.pending_approvals.lock().await;
                pending.insert(
                    patch.id.clone(),
                    PendingApproval::Patch {
                        patch_id: patch.id.clone(),
                        changes: patch.changes.clone(),
                    },
                );
            }

            // Send patch request event
            self.send_event(submission_id, EventType::PatchRequest {
                patch_id: patch.id,
                description: patch.description,
                changes: patch.changes,
                requires_approval: true,
            }).await
        } else {
            // Apply immediately
            self.apply_patch(submission_id, patch.id, patch.changes).await
        }
    }

    async fn handle_execution_result(&mut self, submission_id: arien_common::SubmissionId, result: crate::ExecutionResult) -> Result<()> {
        match result {
            crate::ExecutionResult::Completed { exit_code, stdout, stderr, duration_ms } => {
                // Send stdout
                if !stdout.is_empty() {
                    self.send_event(submission_id, EventType::ExecOutput {
                        output: stdout,
                        is_stderr: false,
                    }).await?;
                }

                // Send stderr
                if !stderr.is_empty() {
                    self.send_event(submission_id, EventType::ExecOutput {
                        output: stderr,
                        is_stderr: true,
                    }).await?;
                }

                self.send_event(submission_id, EventType::ExecCompleted {
                    exit_code,
                    duration_ms,
                }).await
            }
            crate::ExecutionResult::Failed { error } => {
                self.send_event(submission_id, EventType::Error {
                    error,
                    recoverable: true,
                }).await
            }
            crate::ExecutionResult::NeedsApproval { .. } => {
                // This should have been handled earlier
                Ok(())
            }
        }
    }
}

/// Information about a tool call extracted from model response
#[derive(Debug, Clone)]
struct ToolCallInfo {
    id: arien_common::ToolCallId,
    name: String,
    arguments: std::collections::HashMap<String, serde_json::Value>,
}

/// Information about a patch extracted from model response
#[derive(Debug, Clone)]
struct PatchInfo {
    id: String,
    description: String,
    changes: Vec<FileChange>,
}
