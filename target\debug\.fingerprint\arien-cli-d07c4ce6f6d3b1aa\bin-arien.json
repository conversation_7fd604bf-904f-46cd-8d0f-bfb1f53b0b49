{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[]", "target": 10155445189781119005, "profile": 17672942494452627365, "path": 15162269893553335056, "deps": [[4941763578878038180, "arien_linux_sandbox", false, 7565486424552813790], [8606274917505247608, "tracing", false, 7118938455684139428], [9538054652646069845, "tokio", false, 3840114820292566334], [9676459985605165425, "arien_common", false, 14220483938475243701], [11468999454667189829, "clap", false, 13066000903077522141], [11699941782375267194, "arien_core", false, 16466362778259590286], [13625485746686963219, "anyhow", false, 17220651729097877131], [14239018602011489892, "arien_mcp_server", false, 3891562060717562349], [15367738274754116744, "serde_json", false, 2738634569744250569], [16230660778393187092, "tracing_subscriber", false, 1820683818112017796]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\arien-cli-d07c4ce6f6d3b1aa\\dep-bin-arien", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}